package com.example.greetingcard

import android.graphics.Bitmap
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import java.util.regex.Pattern

class ReceiptScanner {
    
    private val textRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
    private val textProcessor = TextProcessor()
    
    fun scanReceipt(bitmap: Bitmap, callback: (ReceiptData) -> Unit) {
        val image = InputImage.fromBitmap(bitmap, 0)
        
        textRecognizer.process(image)
            .addOnSuccessListener { visionText ->
                val rawText = visionText.text
                val receiptData = extractReceiptFieldsWithML(rawText)
                callback(receiptData)
            }
            .addOnFailureListener { e ->
                callback(ReceiptData(rawText = "Error scanning receipt: ${e.message}"))
            }
    }
    
    private fun extractReceiptFieldsWithML(text: String): ReceiptData {
        val lines = textProcessor.preprocessText(text)
        
        val merchantName = extractBestMerchant(lines)
        val date = extractBestDate(lines)
        val amount = extractBestAmount(lines)
        
        return ReceiptData(
            merchantName = merchantName,
            date = date,
            amount = amount,
            rawText = text
        )
    }
    
    private fun extractBestMerchant(lines: List<String>): String? {
        val candidates = textProcessor.extractMerchantCandidates(lines)
        return candidates.firstOrNull()?.text
    }
    
    private fun extractBestDate(lines: List<String>): String? {
        val candidates = textProcessor.extractDateCandidates(lines)
        return candidates.firstOrNull()?.text
    }
    
    private fun extractBestAmount(lines: List<String>): String? {
        val candidates = textProcessor.extractAmountCandidates(lines)
        return candidates.firstOrNull()?.text
    }
    
}