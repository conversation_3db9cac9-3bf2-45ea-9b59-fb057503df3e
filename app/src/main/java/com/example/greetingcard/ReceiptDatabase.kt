package com.example.greetingcard

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import android.content.Context
import java.util.Date

@TypeConverters(DateConverter::class)
@Database(
    entities = [ReceiptEntity::class],
    version = 1,
    exportSchema = false
)
abstract class ReceiptDatabase : RoomDatabase() {
    
    abstract fun receiptDao(): ReceiptDao
    
    companion object {
        @Volatile
        private var INSTANCE: ReceiptDatabase? = null
        
        fun getDatabase(context: Context): ReceiptDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    ReceiptDatabase::class.java,
                    "receipt_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}

class DateConverter {
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }
    
    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }
}